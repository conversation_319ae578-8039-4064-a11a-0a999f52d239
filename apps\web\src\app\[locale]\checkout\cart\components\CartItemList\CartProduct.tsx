import { useCallback, useEffect, useMemo, useState } from 'react'
import Image from 'next/image'
import { useTranslations } from 'next-intl'
import {
  IconStoreTag,
  NCoinRange,
  NCoinView,
  PriceRange,
  PriceRanges,
  TCartProductItem,
  useLazyGetProductLimitQuery,
} from '@ninebot/core'
import { useCartProduct } from '@ninebot/core/src/businessHooks'
import type { ExtensionInfo } from '@ninebot/core/src/businessHooks/useCartProduct'
import { Badge, Button } from 'antd'

import { IconArrow, QuantityButton, Trash } from '@/components'
import { Link } from '@/i18n/navigation'
import type { Product } from '@/types/cart'
import type { ProductLimitData } from '@/types/product'

import { useCart } from '../../context/cartContext'

// 限购标识样式
const BADGE_STYLE = {
  backgroundColor: '#FEE5E5',
  color: '#DA291C',
  height: '23px',
  fontSize: '14px',
  padding: '2px 8px',
} as const

// 自定义选项接口
interface CustomOption {
  label: string
  value: string | number | Big
}

// 组件属性接口
interface CartProductProps {
  rawProduct: TCartProductItem
  quantity: number
  id: string
  onQtyChange: (value: number) => void
  onDelete: (id: string) => void
  isIncrementDisabled?: boolean
}

/**
 * 购物车产品组件
 */
export default function CartProduct({
  rawProduct,
  id,
  quantity,
  onQtyChange,
  onDelete,
  isIncrementDisabled,
}: CartProductProps) {
  const { product, extension_info, parent_sku } = rawProduct
  const getI18nString = useTranslations('Common')
  const { setCartState } = useCart()

  // 限购相关状态
  const [limitCount, setLimitCount] = useState<number | null>(null)
  const [getProductLimit] = useLazyGetProductLimitQuery()

  const {
    isPickupProduct,
    isConfigurableProduct,
    configurableProductOptionValue,
    pickupStoreName,
    isStatus,
    isStock,
    hasCustomOptions,
    customOptionsFormatted,
    customAttributes,
    isDisplayNCoin,
    isPureNCoin,
  } = useCartProduct(
    product as unknown as TCartProductItem['product'],
    extension_info as unknown as ExtensionInfo,
  )

  /**
   * 产品是否可用
   */
  const isAvailable = useMemo(() => {
    return isStatus && isStock
  }, [isStatus, isStock])

  /**
   * 最大购买数量（考虑库存和限购）
   */
  const maxQuantity = useMemo(() => {
    const salableQty = Math.min(product.salable_qty || 99, 99)
    return limitCount ? Math.min(limitCount, salableQty) : salableQty
  }, [limitCount, product.salable_qty])

  /**
   * 获取限购数量
   */
  const getLimitCount = useCallback((data: ProductLimitData) => {
    const now = new Date().getTime()
    if (
      data?.limit &&
      now / 1000 >= (data?.start_time || 0) &&
      now / 1000 <= (data?.end_time || 0)
    ) {
      setLimitCount(Number(data?.limit_qty))
    } else {
      setLimitCount(null)
    }
  }, [])

  /**
   * 获取产品限购信息
   */
  useEffect(() => {
    // 只有当产品可用且有产品ID时才查询限购信息
    if (isAvailable && product?.id) {
      getProductLimit({
        input: { product_id: Number(product?.id) },
      })
        .unwrap()
        .then((res) => {
          getLimitCount(res?.product_purchase_limit as ProductLimitData)
        })
        .catch((error) => {
          console.log('获取限购信息失败:', error)
        })
    }
  }, [product, isAvailable, getProductLimit, getLimitCount])

  /**
   * 修改 qty 操作
   */
  const handleQtyChange = (value: number) => {
    onQtyChange(value)
  }

  /**
   * 删除操作
   */
  const handleDelete = useCallback(() => {
    onDelete(id)
  }, [onDelete, id])

  /**
   * 选择规格操作
   */
  const handlePopup = useCallback(
    (productInfo: Product, parentSku: string, qty: number) => {
      setCartState((prev) => {
        return {
          ...prev,
          item_id: id,
          product: rawProduct,
          parentSku,
          sku: productInfo.sku,
          quantity: qty,
          popVisible: true,
          extensionInfo: extension_info,
        }
      })
    },
    [setCartState, extension_info, id, rawProduct],
  )

  return (
    <div className="flex flex-1 gap-base-24">
      {/* 商品图片 */}
      <div className="responsive-cart-img relative overflow-hidden rounded-3xl">
        <Image
          src={product.image?.url || ''}
          alt={product.image?.label || 'product image'}
          fill
          className="object-contain"
          priority
        />
        {/* 商品状态信息（无货、下架等） */}
        {!isStatus && (
          <div className="absolute bottom-0 left-0 right-0 bg-[#00000014] py-2 text-center text-sm">
            {getI18nString('product_off')}
          </div>
        )}
        {isStatus && !isStock && (
          <div className="absolute bottom-0 left-0 right-0 bg-[#00000014] py-2 text-center text-sm">
            {getI18nString('out_of_stock')}
          </div>
        )}
      </div>

      {/* 商品信息 */}
      <div className="flex flex-1 flex-col justify-between">
        <div>
          {/* 商品标题 */}
          <Link
            className="mb-base-12 flex cursor-pointer items-center"
            href={`/${product.url_key}${product.url_suffix}`}>
            {isPickupProduct && <IconStoreTag />}
            <span className="line-clamp-2 inline font-miSansDemiBold450 text-lg transition-colors hover:text-primary">
              {product.name}
            </span>
          </Link>

          {/* 商品规格 */}
          {isAvailable &&
            (configurableProductOptionValue.length || pickupStoreName || hasCustomOptions) && (
              <div
                onClick={() =>
                  handlePopup(product as unknown as Product, parent_sku || '', quantity)
                }
                className="mb-base-12 flex max-w-max cursor-pointer items-center gap-8 rounded-lg bg-gray-base px-4 py-2 text-base">
                <div className="flex-1 text-left">
                  {isConfigurableProduct && (
                    <span className="text-gray-600">
                      {configurableProductOptionValue.join('，')}
                    </span>
                  )}
                  {isConfigurableProduct && isPickupProduct && pickupStoreName && (
                    <span className="text-gray-400"> | </span>
                  )}
                  {isPickupProduct && pickupStoreName && (
                    <span className="text-gray-600">{pickupStoreName}</span>
                  )}
                  {hasCustomOptions && (
                    <>
                      {customOptionsFormatted.map((customOption: CustomOption) => (
                        <span key={customOption.label}>
                          {(configurableProductOptionValue.length || pickupStoreName) && (
                            <span className="text-gray-400"> | </span>
                          )}
                          <span className="text-gray-600">{customOption.label}：</span>
                          <span className="text-gray-600">{String(customOption.value)}</span>
                        </span>
                      ))}
                    </>
                  )}
                </div>
                <IconArrow rotate={-90} />
              </div>
            )}

          {/* 价格显示 - 支持原价和促销价 */}
          {isAvailable && (
            <div className="flex items-center gap-2">
              {isPureNCoin ? (
                <NCoinRange
                  priceRange={product.price_range as PriceRanges}
                  iconStyle={{ size: 16 }}
                  textStyle="text-lg"
                />
              ) : (
                <div className="flex items-center gap-2">
                  <PriceRange
                    priceRange={product.price_range as PriceRanges}
                    direction="row"
                    textStyle="text-lg"
                  />
                  {isDisplayNCoin && (
                    <NCoinView
                      number={Number(customAttributes.max_usage_limit_ncoins)}
                      prefixText={getI18nString('product_more_use')}
                      iconStyle={{ size: 16 }}
                      textStyle="text-lg"
                    />
                  )}
                </div>
              )}
            </div>
          )}

          {/* 商品状态操作（无货、下架等） */}
          {!isAvailable && (
            <div className="mt-4 rounded-lg bg-gray-50 p-3">
              {!isStatus ? (
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{getI18nString('product_down')}</span>
                  <Button danger onClick={handleDelete}>
                    {getI18nString('delete')}
                  </Button>
                </div>
              ) : !isStock && parent_sku === product?.sku ? (
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{getI18nString('product_out_of_stock')}</span>

                  <Button danger onClick={handleDelete}>
                    {getI18nString('delete')}
                  </Button>
                </div>
              ) : !isStock && parent_sku !== product?.sku ? (
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{getI18nString('cart_reselect')}</span>
                  <Button
                    type="primary"
                    onClick={() =>
                      handlePopup(product as unknown as Product, parent_sku || '', quantity)
                    }>
                    {getI18nString('reselect')}
                  </Button>
                </div>
              ) : null}
            </div>
          )}
        </div>

        {/* 商品数量操作 */}
        {isAvailable && (
          <div className="mt-6 flex items-center justify-between">
            <div className="flex items-center gap-base">
              <QuantityButton
                quantity={quantity}
                onQuantityChange={handleQtyChange}
                maxQuantity={maxQuantity}
                minQuantity={1}
                small
                isIncrementDisabled={isIncrementDisabled}
              />
              {limitCount && <Badge count={`限购${limitCount}件`} style={BADGE_STYLE} />}
            </div>

            <button onClick={handleDelete} className="flex h-12 w-12 items-center justify-center">
              <Trash />
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
