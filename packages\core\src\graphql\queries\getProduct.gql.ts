import { gql } from '../../utils'

/**
 * 获取商品详情信息
 */
export const GET_PRODUCTS = gql`
  query getProducts(
    $filter: ProductAttributeFilterInput
    $customAttributesV3Filter1: AttributeFilterInput
    $customAttributesV3Filter2: AttributeFilterInput
    $customAttributesV3Filter3: AttributeFilterInput
  ) {
    products(filter: $filter) {
      items {
        ... on CustomizableProductInterface {
          options {
            option_id
            title
            required
            ... on CustomizableDropDownOption {
              dropDown: value {
                sku
                uid
                title
                price
              }
            }
          }
        }
        ... on BundleProduct {
          items {
            option_id
            sku
            title
            required
            options {
              product {
                short_description {
                  html
                }
                custom_attributesV3(filters: $customAttributesV3Filter1) {
                  items {
                    code
                    ... on AttributeValue {
                      value
                    }
                  }
                }
              }
              label
              id
              quantity
              price
            }
          }
        }
        ... on ConfigurableProduct {
          configurable_options {
            attribute_id
            product_id
            label
            attribute_code
            values {
              uid
              default_label
              label
              store_label
              use_default_value
              value_index
              swatch_data {
                ... on ImageSwatchData {
                  thumbnail
                }
                value
              }
            }
          }
          variants {
            attributes {
              label
              code
              uid
              value_index
            }
            product {
              __typename
              sku
              name
              id
              uid
              type_id
              image {
                url
              }
              salable_qty
              status
              stock_status
              paymeng_method
              delivery_method
              media_gallery {
                url
                label
                disabled
                position
              }
              custom_attributesV3(filters: $customAttributesV3Filter2) {
                items {
                  code
                  ... on AttributeValue {
                    value
                  }
                  ... on AttributeSelectedOptions {
                    selected_options {
                      label
                      value
                    }
                  }
                }
              }
              price_range {
                maximum_price {
                  discount {
                    amount_off
                  }
                  final_price {
                    value
                  }
                  regular_price {
                    value
                  }
                }
              }
            }
          }
        }
        sku
        id
        uid
        name
        type_id
        digital_has_audio
        status
        stock_status
        mb_description
        video_image
        video_url
        salable_qty
        mb_after_sales_instructions
        after_sales_instructions
        description {
          html
        }
        related_products {
          __typename
          sku
        }
        customer_reviews(pageSize: 1, currentPage: 1) {
          items {
            avatar
            is_best
            nickname
            return_comment
            return_date
            review
            entity_id
            review_date
            score
          }
          page_info {
            total_pages
          }
        }
        price_range {
          maximum_price {
            final_price {
              value
            }
            regular_price {
              value
            }
          }
        }
        image {
          url
        }
        media_gallery {
          url
          label
          position
          disabled
        }
        __typename
        paymeng_method
        delivery_method
        custom_attributesV3(filters: $customAttributesV3Filter3) {
          items {
            code
            ... on AttributeValue {
              value
            }
            ... on AttributeSelectedOptions {
              selected_options {
                label
                value
              }
            }
          }
        }
      }
    }
  }
`

/**
 * 获取门店信息
 */
export const GET_PRODUCT_STORES = gql`
  query productStores($input: GetProductStoresInput!) {
    product_stores(input: $input) {
      product_id
      store_address
      business_hours: store_business_hours
      closing_hours: store_closing_hours
      store_code
      store_distance
      store_id
      store_latitude
      store_longitude
      store_name
      store_phone
      price: store_price
      store_type {
        code
        label
      }
      vehicle_type {
        code
        label
      }
    }
  }
`

/**
 * 获取门店列表v2
 */
export const GET_PRODUCT_STORES_V2 = gql`
  query productStoresV2($input: GetProductStoresInput!) {
    product_storesV2(input: $input) {
      items {
        product_id
        store_address
        business_hours: store_business_hours
        closing_hours: store_closing_hours
        store_code
        store_distance
        store_id
        store_latitude
        store_longitude
        store_name
        store_phone
        price: store_price
        store_type {
          code
          label
        }
        vehicle_type {
          code
          label
        }
        review_rating
        review_count
        review_details_link_app
      }
      message
    }
  }
`

/**
 * 加入购物车
 */
export const ADD_PRODUCT_TO_CART = gql`
  mutation AddProductsToShippingCart($input: [shippingCartItemsInput!]!) {
    addProductsToShippingCart(items: $input) {
      items_count
      items {
        quantity
        uid
        is_selected
        product {
          sku
          name
          type_id
        }
      }
    }
  }
`

/**
 * 立即购买
 */
export const BUY_NOW_TO_CHECKOUT = gql`
  mutation BuyNowToCheckout($input: [shippingCartItemsInput!]!) {
    buyNowToCheckout(items: $input) {
      status
      cart {
        id
        is_virtual
        is_pickup
      }
    }
  }
`
/**
 * 获取当前用户的设备信息
 */
export const GET_DECIVCE_INFO = gql`
  query getDecivceInfo {
    getCurrentUserDecivceInfo {
      category
      category_code
      cycleNo
      decivceName
      gpsNo
      manufacturer
      nickName
      productImage
      has_active_service_package
      snNo
      vin
    }
  }
`
