/* --------------------------------- 通用验证规则 -------------------------------- */
import * as Yup from 'yup'

// import { getI18nString } from '../i18n';

// 创建一个消息字典，用于表单验证
const messages = {
  form_required_phone: '手机号是必填项！',
  form_valid_phone: '请输入正确的手机号',
  form_required_id_card: '请输入身份证号',
  form_valid_id_card: '请输入正确的身份证号',
  // 可以根据需要添加更多消息
}

// 获取国际化字符串的函数
const getI18nString = (key: keyof typeof messages, ...args: (string | number)[]) => {
  let message = messages[key] || key

  // 替换参数
  args.forEach((arg, index) => {
    message = message.replace(`\${${index}}`, String(arg))
  })

  return message
}

/**
 * 中国手机号验证规则
 */
export const chinaPhoneSchema = Yup.string()
  .required(getI18nString('form_required_phone'))
  .test('is-valid-phone', getI18nString('form_valid_phone'), (value) => {
    if (!value) {
      return false
    }
    const rawPhone = value.replace(/\s/g, '')
    const phoneRegex =
      /^(0|86|17951)?(13[0-9]|14[579]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])[0-9]{8}$/
    return phoneRegex.test(rawPhone)
  })

/**
 * 身份证号验证规则
 */
export const idCardSchema = Yup.string()
  .trim()
  .required(getI18nString('form_required_id_card'))
  .matches(
    /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/,
    getI18nString('form_valid_id_card'),
  )
