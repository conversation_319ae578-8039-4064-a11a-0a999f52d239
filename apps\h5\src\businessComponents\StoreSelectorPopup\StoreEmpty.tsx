import { useCallback, useMemo } from 'react'
import { useTranslations } from 'next-intl'
import { ErrorBlock } from 'antd-mobile'

import { Arrow } from '@/components'

// ==================== 图标组件 ====================

/**
 * 门店空状态图标
 */
const StoreEmptyIcon = () => (
  <svg
    className="inline-block"
    width="108"
    height="108"
    viewBox="0 0 108 108"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_52726_5043)">
      <rect width="108" height="108" fill="white" />
      <g clipPath="url(#clip1_52726_5043)">
        <rect width="108" height="108" fill="white" />
        <path
          d="M26.4008 81.5998H81.6008L82.2008 46.7998H25.8008L26.4008 81.5998Z"
          fill="#BBBBBD"
        />
        <path d="M40.8008 81.5998H54.0008V64.7998H40.8008V81.5998Z" fill="#F8F8F9" />
        <path d="M82.8 25.2002H25.8L21 46.8002H25.8H82.2H87L82.8 25.2002Z" fill="#F3F3F4" />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_52726_5043">
        <rect width="108" height="108" fill="white" />
      </clipPath>
      <clipPath id="clip1_52726_5043">
        <rect width="108" height="108" fill="white" />
      </clipPath>
    </defs>
  </svg>
)

/**
 * 门店选择器空状态图标
 */
const StoreSelectorEmptyIcon = () => (
  <svg
    className="inline-block"
    width="109"
    height="109"
    viewBox="0 0 109 109"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_52726_65013)">
      <rect width="108" height="108" transform="translate(0.5 0.5)" fill="white" />
      <g clipPath="url(#clip1_52726_65013)">
        <rect width="108" height="108" transform="translate(0.5 0.5)" fill="white" />
        <path
          d="M69.4981 81.1254L67.698 65.5254L54.498 67.9254V85.9254L69.4981 81.1254Z"
          fill="#E1E1E4"
        />
        <path
          d="M39.498 81.1254L41.298 65.5254L30.498 67.9254L19.6992 84.7254L39.498 81.1254Z"
          fill="#E1E1E4"
        />
        <path
          d="M67.698 65.5254L69.4981 81.1254L89.2992 84.7254L78.4981 67.9254L67.698 65.5254Z"
          fill="#F3F3F4"
        />
        <path
          d="M41.298 65.5254L39.498 81.1254L54.498 85.9254V67.9254L41.298 65.5254Z"
          fill="#F3F3F4"
        />
        <path
          d="M34.1016 45.8076C34.1016 34.7638 43.6284 25.9248 54.5016 25.9248C65.3747 25.9248 74.9016 34.7638 74.9016 45.8076C74.9016 53.5737 69.3605 60.4452 64.9742 65.0863C62.2666 67.9512 57.7522 71.707 54.5016 73.9229C51.2444 71.6986 46.7441 67.9605 44.0262 65.0848C39.6399 60.4438 34.1016 53.5737 34.1016 45.8076Z"
          fill="#BBBBBD"
        />
        <path
          d="M45.8008 46.325C45.8008 41.6858 49.8616 37.625 54.5008 37.625C59.1399 37.625 63.2008 41.6858 63.2008 46.325C63.2008 50.9642 59.1399 54.725 54.5008 54.725C49.8616 54.725 45.8008 50.9642 45.8008 46.325Z"
          fill="#F8F8F9"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_52726_65013">
        <rect width="108" height="108" fill="white" transform="translate(0.5 0.5)" />
      </clipPath>
      <clipPath id="clip1_52726_65013">
        <rect width="108" height="108" fill="white" transform="translate(0.5 0.5)" />
      </clipPath>
    </defs>
  </svg>
)

// ==================== 类型定义 ====================

/**
 * 组件属性接口
 */
interface EmptyProps {
  isStore?: boolean
  isStoreSelector?: boolean
  onOpenAddressPop: () => void
  storeMsg?: string
}

/**
 * 空状态配置接口
 */
interface EmptyConfig {
  description: string
  action?: {
    text: string
    onClick: () => void
  }
}

// ==================== 主组件 ====================

/**
 * 空状态组件
 *
 * 功能：
 * 1. 显示门店列表为空的状态
 * 2. 显示定位权限被拒绝的状态
 * 3. 提供相应的操作按钮
 */
export default function Empty({
  isStore = false,
  isStoreSelector = false,
  onOpenAddressPop,
  storeMsg,
}: EmptyProps) {
  const getI18nString = useTranslations('Common')

  // ==================== 计算属性 ====================

  /**
   * 空状态配置
   */
  const emptyConfig = useMemo((): EmptyConfig => {
    if (isStore) {
      return {
        description: storeMsg || getI18nString('product_no_store_tip'),
        action: {
          text: getI18nString('product_change_store'),
          onClick: onOpenAddressPop,
        },
      }
    }

    if (isStoreSelector) {
      return {
        description: '目前还没有定位权限哦～',
      }
    }

    return {
      description: '',
    }
  }, [isStore, isStoreSelector, getI18nString, onOpenAddressPop, storeMsg])

  /**
   * 获取对应的图标组件
   */
  const getEmptyIcon = useMemo(() => {
    if (isStore) return <StoreEmptyIcon />
    if (isStoreSelector) return <StoreSelectorEmptyIcon />
    return <span />
  }, [isStore, isStoreSelector])

  // ==================== 渲染函数 ====================

  /**
   * 渲染操作按钮
   */
  const renderAction = useCallback(() => {
    if (!emptyConfig.action) return null

    const { text, onClick } = emptyConfig.action
    return (
      <button
        onClick={onClick}
        className="mt-4 flex items-center justify-center text-lg text-font-primary">
        {text}
        <Arrow className="-rotate-90" />
      </button>
    )
  }, [emptyConfig.action])

  /**
   * 渲染描述文案
   */
  const renderDescription = useCallback(() => {
    if (!emptyConfig.description) return null

    return <div className="text-[14px] text-gray-3">{emptyConfig.description}</div>
  }, [emptyConfig.description])

  // ==================== 组件返回 ====================

  return (
    <div className="my-48 flex items-center justify-center">
      <ErrorBlock
        style={{
          '--image-height': 'auto',
          '--image-width': 'auto',
          margin: 'auto',
        }}
        title=""
        image={getEmptyIcon}
        description={
          <div className="flex flex-col items-center justify-center">
            {renderDescription()}
            {renderAction()}
          </div>
        }
      />
    </div>
  )
}
