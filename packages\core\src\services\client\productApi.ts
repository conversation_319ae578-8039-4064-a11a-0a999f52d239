import {
  ADD_PRODUCT_TO_CART,
  BUY_NOW_TO_CHECKOUT,
  GET_DECIVCE_INFO,
  GET_PRODUCT_COUPONS,
  GET_PRODUCT_LIMIT,
  GET_PRODUCT_SOUND,
  GET_PRODUCT_STORES,
  GET_PRODUCT_STORES_V2,
  GET_PRODUCTS,
} from '../../graphql'
import {
  AddProductsToShippingCartMutation,
  AddProductsToShippingCartMutationVariables,
  BuyNowToCheckoutMutation,
  BuyNowToCheckoutMutationVariables,
  GetDecivceInfoQuery,
  GetDecivceInfoQueryVariables,
  GetProductCouponsQuery,
  GetProductCouponsQueryVariables,
  GetProductLimitQuery,
  GetProductLimitQueryVariables,
  GetProductSoundQuery,
  GetProductSoundQueryVariables,
  GetProductsQuery,
  GetProductsQueryVariables,
  ProductStoresQuery,
  ProductStoresQueryVariables,
  ProductStoresV2Query,
  ProductStoresV2QueryVariables,
} from '../../graphql/generated/graphql'
import rootApi from '../../utils/rootApi'

/**
 * product 查询/操作相关 api
 */
const productApi = rootApi.injectEndpoints({
  endpoints: (build) => ({
    /**
     * 查询产品信息
     */
    getProductDetail: build.query<GetProductsQuery, GetProductsQueryVariables>({
      query: (variables) => ({
        document: GET_PRODUCTS,
        variables,
        method: 'POST',
      }),
      extraOptions: { isAuth: false },
    }),

    /**
     * 获取门店信息
     */
    getStore: build.query<ProductStoresQuery, ProductStoresQueryVariables>({
      query: (variables) => ({
        document: GET_PRODUCT_STORES,
        variables,
      }),
      extraOptions: { isAuth: false },
    }),
    /**
     * 获取门店信息
     */
    getStoreV2: build.query<ProductStoresV2Query, ProductStoresV2QueryVariables>({
      query: (variables) => ({
        document: GET_PRODUCT_STORES_V2,
        variables,
      }),
      extraOptions: { isAuth: false },
    }),
    /**
     * 加入购物车
     */
    addToCart: build.mutation<
      AddProductsToShippingCartMutation,
      AddProductsToShippingCartMutationVariables
    >({
      query: (variables) => ({
        document: ADD_PRODUCT_TO_CART,
        variables,
      }),
      extraOptions: { maxRetries: 0, isAuth: true },
    }),

    /**
     * 立即购买
     */
    buyNowCheckout: build.mutation<BuyNowToCheckoutMutation, BuyNowToCheckoutMutationVariables>({
      query: (variables) => ({
        document: BUY_NOW_TO_CHECKOUT,
        variables,
      }),
      extraOptions: { maxRetries: 0, isAuth: true },
    }),
    /**
     * 获取商品限购信息
     */
    getProductLimit: build.query<GetProductLimitQuery, GetProductLimitQueryVariables>({
      query: (variables) => ({
        document: GET_PRODUCT_LIMIT,
        variables,
      }),
      extraOptions: {
        isAuth: true,
      },
    }),
    /**
     * 查询用户当前绑定的设备信息
     */
    getDecivceInfo: build.query<GetDecivceInfoQuery, GetDecivceInfoQueryVariables>({
      query: (variables) => ({
        document: GET_DECIVCE_INFO,
        variables,
      }),
      extraOptions: {
        isAuth: false,
      },
    }),

    /**
     * 获取产品优惠券列表
     */
    getProductCoupons: build.query<GetProductCouponsQuery, GetProductCouponsQueryVariables>({
      query: (variables) => ({
        document: GET_PRODUCT_COUPONS,
        variables,
      }),
    }),
    /**
     * 获取产品音效列表
     */
    getProductSound: build.query<GetProductSoundQuery, GetProductSoundQueryVariables>({
      query: (variables) => ({
        document: GET_PRODUCT_SOUND,
        variables,
      }),
    }),
  }),
})

export const {
  useGetProductDetailQuery,
  useLazyGetProductLimitQuery,
  useLazyGetProductDetailQuery,
  useAddToCartMutation,
  useBuyNowCheckoutMutation,
  useGetStoreQuery,
  useGetDecivceInfoQuery,
  useLazyGetDecivceInfoQuery,
  useLazyGetStoreQuery,
  useLazyGetStoreV2Query,
  useLazyGetProductCouponsQuery,
  useLazyGetProductSoundQuery,
} = productApi

export default productApi
