'use client'

import { useCallback, useMemo } from 'react'
import { useTranslations } from 'next-intl'
import type { StoreListItem } from '@ninebot/core'
import { formatDistance } from '@ninebot/core'
import clsx from 'clsx'

// ==================== 类型定义 ====================

/**
 * 组件属性接口
 */
interface StoreItemProps {
  store: StoreListItem
  isSelected?: boolean
  onClick?: (store: StoreListItem) => void
}

// ==================== 图标组件 ====================

/**
 * 电话图标
 */
const PhoneIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M8.66026 5.32812L6.00391 7.34335C8.66411 14.0012 10.6636 16.0398 16.6636 18.0031L18.6786 15.3464L16.6638 13.3317L15.3319 13.3372L14.5445 14.2154C12 13.46 10.2085 10.6331 9.63695 9.30789L10.6656 8.67081L10.6656 7.33342L8.66026 5.32812Z"
      fill="#444446"
    />
  </svg>
)

/**
 * 位置图标
 */
const LocationIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M11.9993 18.3644C11.9993 18.3644 17.3327 14.8789 17.3327 10.8493C17.3327 7.97585 14.9449 5.64648 11.9993 5.64648C9.05383 5.64648 6.66602 7.97585 6.66602 10.8493C6.66602 14.8789 11.9993 18.3644 11.9993 18.3644Z"
      fill="#444446"
    />
    <path
      d="M12 12.959C13.1046 12.959 14 12.0635 14 10.959C14 9.85442 13.1046 8.95898 12 8.95898C10.8954 8.95898 10 9.85442 10 10.959C10 12.0635 10.8954 12.959 12 12.959Z"
      fill="#F3F3F4"
    />
  </svg>
)

/**
 * 导航图标
 */
const NavigationIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.8433 6.79093C17.9206 6.5747 17.8571 6.33321 17.6834 6.18302C17.5097 6.03284 17.2615 6.00484 17.0587 6.11254L6.52262 11.7079C6.31364 11.8189 6.19776 12.05 6.23379 12.2838C6.26982 12.5177 6.44987 12.7031 6.68256 12.7461L11.2759 13.5937L12.7782 18.0165C12.8543 18.2405 13.0638 18.3919 13.3004 18.3938C13.537 18.3956 13.7489 18.2476 13.8286 18.0248L17.8433 6.79093Z"
      fill="#444446"
    />
  </svg>
)

/**
 * 选中图标
 */
const SelectIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M4.16707 8.4045L2.98856 9.58301L7.21427 13.8089L8.39278 13.8089L8.39278 12.6304L4.16707 8.4045Z"
      fill="#DA291C"
    />
    <path
      d="M17.5477 6.17851L16.3691 5L8.73858 12.6307L8.73858 13.8092L9.91709 13.8092L17.5477 6.17851Z"
      fill="#DA291C"
    />
  </svg>
)

// ==================== 主组件 ====================

/**
 * 门店列表项组件
 *
 * 功能：
 * 1. 显示门店基本信息
 * 2. 支持选中状态
 * 3. 显示营业时间、距离、地址等信息
 */
const StoreItem = ({ store, isSelected, onClick }: StoreItemProps) => {
  const getI18nString = useTranslations('Common')

  // ==================== 计算属性 ====================

  /**
   * 容器类名
   */
  const containerClassName = useMemo(
    () =>
      clsx('flex cursor-pointer items-center justify-between rounded-base-12 border-2 p-4', {
        'border-primary': isSelected,
        'border-gray-200': !isSelected,
      }),
    [isSelected],
  )

  /**
   * 营业时间文本
   */
  const businessHoursText = useMemo(() => {
    return `${store.business_hours}-${store.closing_hours}`
  }, [store.business_hours, store.closing_hours])

  /**
   * 格式化距离
   */
  const formattedDistance = useMemo(() => {
    return Number(store.store_distance) > 0 ? formatDistance(Number(store.store_distance)) : null
  }, [store.store_distance])

  // ==================== 事件处理函数 ====================

  /**
   * 点击事件处理
   */
  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault()
      e.stopPropagation()
      onClick?.(store)
    },
    [onClick, store],
  )

  // ==================== 渲染函数 ====================

  /**
   * 渲染营业时间信息
   */
  const renderBusinessHours = useCallback(
    () => (
      <div className="flex items-center gap-base">
        <PhoneIcon />
        <span>
          {getI18nString('product_store_business')} {businessHoursText}
        </span>
      </div>
    ),
    [getI18nString, businessHoursText],
  )

  /**
   * 渲染距离信息
   */
  const renderDistance = useCallback(() => {
    if (!formattedDistance) return null

    return (
      <div className="flex items-center gap-base">
        <NavigationIcon />

        <span>
          {getI18nString('product_store_distance')} {formattedDistance}
        </span>
      </div>
    )
  }, [formattedDistance, getI18nString])

  /**
   * 渲染地址信息
   */
  const renderAddress = useCallback(
    () => (
      <div className="flex items-center gap-base">
        <LocationIcon />
        <span>{store.store_address}</span>
      </div>
    ),
    [store.store_address],
  )

  /**
   * 渲染选中状态
   */
  const renderSelectionState = useCallback(() => {
    if (!isSelected) return null

    return (
      <div className="flex items-center gap-2">
        <SelectIcon />
      </div>
    )
  }, [isSelected])

  // ==================== 组件返回 ====================

  return (
    <div onClick={handleClick} className={containerClassName}>
      {/* 门店信息 */}
      <div className="space-y-base-16">
        {/* 门店名称 */}
        <div className="text-[18px] leading-[1.2]">{store.store_name}</div>

        {/* 门店详情 */}
        <div className="flex flex-col gap-base font-miSansRegular330 text-[14px] leading-[1.4] text-[#444446]">
          {renderBusinessHours()}
          {renderDistance()}
          {renderAddress()}
        </div>
      </div>

      {/* 选中状态 */}
      {renderSelectionState()}
    </div>
  )
}

export default StoreItem
